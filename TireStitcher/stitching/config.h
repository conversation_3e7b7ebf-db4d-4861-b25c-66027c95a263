#ifndef STITCH_TIRE_CONFIG_H
#define STITCH_TIRE_CONFIG_H

#include <string>
#include <mutex>
#include <iostream>

// Mutex for thread-safe output
extern std::mutex coutMutex;

// Thread-safe print function
template<typename T>
void safePrint(const T& message, bool force = false, const struct StitchConfig& config = {});

// Resolution-specific parameter sets
struct ResolutionParams {
    // Strip width and overlap parameters
    double stripWidthMargin;        // Margin percentage for strip width calculation
    double overlapPercentage;       // Overlap as percentage of movement
    int fallbackStripWidth;         // Fallback strip width when calculation fails
    int minOverlapPixels;           // Minimum overlap in pixels for large images

    // Template matching parameters
    int templateMatchPrecision;     // 0=fast, 1=medium, 2=high precision
    double correlationThreshold;    // Minimum correlation for template matching
    double highCorrelationThreshold; // Threshold for high-quality matches

    // Movement detection parameters
    double opticalFlowScale;        // Scale factor for optical flow processing
    double smallMovementThreshold;  // Threshold for small movement detection
    double defaultMovementBase;     // Base value for default movement calculation
    double defaultMovementScale;    // Scale factor for default movement

    // Performance parameters
    int framesPerTile;             // Number of frames per processing tile
    int movingAverageSize;         // Size of moving average buffer
    int preloadBatchSize;          // Number of images to preload

    // Quality parameters
    int jpegQuality;               // JPEG compression quality
    bool useEnhancedBlending;      // Use enhanced blending algorithms
};

struct StitchConfig {
    bool verbose;
    int jpegQuality;
    int templateMatchPrecision;
    double resizeScale;
    bool useHierarchicalStitching; // Neue Option für hierarchisches Stitching
    bool keepTemporaryFiles;
    std::string resolutionMode; // Added for 4K/8K differentiation
    int stripWidth; // Added for configurable strip width

    // Resolution-specific parameters
    ResolutionParams currentParams; // Current active parameter set

    StitchConfig() :
        verbose(false),
        jpegQuality(98),
        templateMatchPrecision(2),
        resizeScale(1.0),
        useHierarchicalStitching(true),
        keepTemporaryFiles(false),
        resolutionMode("8K"), // Default to 8K
        stripWidth(0) // Default to 0, indicating not set by CLI for 4K special handling
    {
        // Initialize with 8K parameters by default
        applyResolutionParams();
    }

    // Apply resolution-specific parameters based on resolutionMode
    void applyResolutionParams();

    // Get 4K optimized parameters
    static ResolutionParams get4KParams();

    // Get 8K optimized parameters
    static ResolutionParams get8KParams();
};

// Thread-safe print function implementation
// Modified to not output to console
template<typename T>
void safePrint(const T& message, bool force, const StitchConfig& config) {
    // Disabled console output as per requirements
    // Only critical errors will be logged through the Python error handler
    return;
}

#endif // STITCH_TIRE_CONFIG_H