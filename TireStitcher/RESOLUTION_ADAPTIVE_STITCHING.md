# Resolution-Adaptive Panorama Stitching System

## Overview

The ReifenScanner now features a dual-mode resolution-adaptive stitching system that automatically optimizes panorama stitching parameters based on video resolution. The system can automatically detect whether the input video is 4K (3840x2160) or 8K (7680x4320) and apply the appropriate parameter set for optimal quality and performance.

## Key Features

### 1. Automatic Resolution Detection
- **Auto-detection**: Automatically analyzes input video files using ffprobe to determine resolution
- **Fallback**: Defaults to 8K mode if detection fails or no video is provided
- **Manual Override**: Command-line option to manually specify resolution mode

### 2. Resolution-Specific Parameter Sets

#### 4K Mode (3840x2160) - Optimized for Speed
- **Strip Width Margin**: 15% (vs 10% for 8K)
- **Overlap Percentage**: 10% of movement
- **Fallback Strip Width**: 20 pixels
- **Min Overlap Pixels**: 5 pixels
- **Template Match Precision**: 1 (medium precision for speed)
- **Correlation Threshold**: 0.75 (slightly lower for faster processing)
- **High Correlation Threshold**: 0.90
- **Optical Flow Scale**: 0.3 (less downscaling needed)
- **Small Movement Threshold**: 3.0 pixels
- **Default Movement**: 50.0 + 8.0 * sqrt(movement)
- **Frames Per Tile**: 150 (more frames per tile)
- **Moving Average Size**: 3 (smaller buffer for responsiveness)
- **Preload Batch Size**: 25 images
- **JPEG Quality**: 95%
- **Enhanced Blending**: Disabled (for speed)

#### 8K Mode (7680x4320) - Optimized for Quality
- **Strip Width Margin**: 10%
- **Overlap Percentage**: 10% of movement
- **Fallback Strip Width**: 50 pixels
- **Min Overlap Pixels**: 20 pixels
- **Template Match Precision**: 2 (high precision for quality)
- **Correlation Threshold**: 0.80 (higher for better quality)
- **High Correlation Threshold**: 0.95
- **Optical Flow Scale**: 0.25 (more downscaling for performance)
- **Small Movement Threshold**: 5.0 pixels
- **Default Movement**: 75.0 + 10.0 * sqrt(movement)
- **Frames Per Tile**: 100 (standard tile size)
- **Moving Average Size**: 5 (larger buffer for stability)
- **Preload Batch Size**: 20 images
- **JPEG Quality**: 98%
- **Enhanced Blending**: Enabled (for quality)

## Implementation Details

### Files Modified

#### Core Configuration
- `stitching/config.h` - Added ResolutionParams structure and methods
- `stitching/resolution_config.cpp` - Implementation of parameter sets
- `stitching/panorama_processor.cpp` - Updated to use resolution-specific parameters
- `stitching/movement_detection.cpp` - Updated thresholds and optical flow parameters
- `stitching/strip_extraction.cpp` - Updated template matching parameters
- `main.cpp` - Simplified parameter application

#### Python Interface
- `core/process_utils.py` - Added auto-detection function and video path parameter
- `core/panorama_processor.py` - Updated to pass video path for detection

#### Build System
- `CMakeLists.txt` - Added resolution_config.cpp to build

### Usage

#### Automatic Mode (Recommended)
```bash
# The system will automatically detect resolution from the video file
stitch_tire.exe --input frames/ --output panorama/ --file-pattern "IMG_*.JPG"
```

#### Manual Override
```bash
# Force 4K mode
stitch_tire.exe --input frames/ --output panorama/ --file-pattern "IMG_*.JPG" --resolution-mode 4K

# Force 8K mode
stitch_tire.exe --input frames/ --output panorama/ --file-pattern "IMG_*.JPG" --resolution-mode 8K
```

## Performance Expectations

### 4K Mode Benefits
- **Faster Processing**: ~25-40% faster due to medium precision template matching
- **Lower Memory Usage**: Smaller tile sizes and reduced precision requirements
- **Good Quality**: Optimized for 4K content while maintaining acceptable quality
- **Responsive**: Smaller moving average for quicker adaptation to changes

### 8K Mode Benefits
- **Maximum Quality**: High precision template matching for best results
- **Stability**: Larger moving average for consistent results
- **Enhanced Blending**: Advanced blending algorithms for seamless panoramas
- **Optimized for Large Images**: Parameters tuned for 8K content

## Backward Compatibility

The system maintains full backward compatibility:
- Existing command-line parameters still work
- Default behavior (8K mode) unchanged when no resolution specified
- Manual parameter overrides still take precedence
- All existing functionality preserved

## Testing

Run the test script to verify the implementation:
```bash
python test_resolution_detection.py
```

## Future Enhancements

1. **Additional Resolutions**: Support for 2K, 6K, and other resolutions
2. **Dynamic Parameter Tuning**: Machine learning-based parameter optimization
3. **Quality Metrics**: Automatic quality assessment and parameter adjustment
4. **Performance Profiling**: Built-in performance monitoring and optimization
